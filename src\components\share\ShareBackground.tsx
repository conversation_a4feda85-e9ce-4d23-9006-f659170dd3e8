'use client'

import Image from 'next/image'

interface ShareBackgroundProps {
  variant: 'rank' | 'win'
}

const ShareBackground: React.FC<ShareBackgroundProps> = ({ variant }) => {
  const imageSrc = variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp'

  return (
    <div className='absolute inset-0'>
      {/* Next.js Image for optimization */}
      <Image
        src={imageSrc}
        alt='Share Background'
        fill
        quality={100}
        className='absolute inset-0 object-cover'
        priority
        onError={(e) => {
          console.warn('Next.js Image failed to load, showing fallback')
          // Hide the failed Next.js image
          e.currentTarget.style.display = 'none'
        }}
      />
      
      {/* Fallback regular img for mobile compatibility */}
      <img
        src={imageSrc}
        alt='Share Background Fallback'
        className='absolute inset-0 h-full w-full object-cover'
        style={{
          zIndex: -1, // Behind the Next.js Image
          display: 'block'
        }}
        onLoad={() => {
          console.log('Fallback background image loaded successfully')
        }}
        onError={() => {
          console.error('Both Next.js Image and fallback img failed to load')
        }}
      />
    </div>
  )
}

export default ShareBackground
