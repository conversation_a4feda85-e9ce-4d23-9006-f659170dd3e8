'use client'

import { useState, useEffect } from 'react'
import { testFontLoading, testFontToBase64, createFontVisualTest, testBackgroundImages } from '@/utils/fontTestUtils'

interface FontDebuggerProps {
  show?: boolean
}

const FontDebugger: React.FC<FontDebuggerProps> = ({ show = false }) => {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(show)

  useEffect(() => {
    if (isVisible) {
      runTests()
    }
  }, [isVisible])

  const runTests = async () => {
    const results: string[] = []

    try {
      // Test font accessibility
      results.push('Testing font file accessibility...')
      await testFontLoading()

      // Test background images
      results.push('Testing background image accessibility...')
      await testBackgroundImages()

      // Test base64 conversion
      results.push('Testing BD Street font base64 conversion...')
      const bdStreetResult = await testFontToBase64('/fonts/BDStreetSignSans_Variable.ttf')
      results.push(`BD Street base64: ${bdStreetResult ? '✅ Success' : '❌ Failed'}`)

      results.push('Testing Signwriter font base64 conversion...')
      const signwriterResult = await testFontToBase64('/fonts/00003-UTM-AS-Signwriter.ttf')
      results.push(`Signwriter base64: ${signwriterResult ? '✅ Success' : '❌ Failed'}`)

      results.push('All tests completed. Check console for detailed logs.')
    } catch (error) {
      results.push(`❌ Test failed: ${error}`)
    }

    setTestResults(results)
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          zIndex: 9999,
          padding: '10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
      >
        Debug Fonts
      </button>
    )
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        width: '400px',
        maxHeight: '500px',
        background: 'white',
        border: '2px solid #ccc',
        borderRadius: '8px',
        padding: '15px',
        zIndex: 9999,
        fontSize: '14px',
        overflow: 'auto',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0 }}>Font Debugger</h3>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            padding: '5px 10px',
            cursor: 'pointer'
          }}
        >
          Close
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={runTests}
          style={{
            background: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            padding: '8px 15px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Run Tests
        </button>
        <button
          onClick={createFontVisualTest}
          style={{
            background: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '3px',
            padding: '8px 15px',
            cursor: 'pointer'
          }}
        >
          Visual Test
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h4>Font Samples:</h4>
        <div className='font-bdstreet' style={{ marginBottom: '5px', fontSize: '18px' }}>
          BD Street Font: Chia sẻ cộng đồng
        </div>
        <div className='font-signwriter' style={{ marginBottom: '5px', fontSize: '18px' }}>
          Signwriter Font: Màn 123
        </div>
        <div className='font-montserrat' style={{ marginBottom: '5px', fontSize: '18px' }}>
          Montserrat Font: Score 12345
        </div>
      </div>

      <div>
        <h4>Test Results:</h4>
        <div
          style={{ background: '#f8f9fa', padding: '10px', borderRadius: '3px', maxHeight: '200px', overflow: 'auto' }}
        >
          {testResults.length === 0 ? (
            <em>No tests run yet. Click "Run Tests" to start.</em>
          ) : (
            testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '5px' }}>
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default FontDebugger
