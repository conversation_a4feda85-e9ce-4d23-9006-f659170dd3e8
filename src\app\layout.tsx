// src/app/layout.tsx
import type { Metadata } from 'next'
import localFont from 'next/font/local'
import { Montser<PERSON> } from 'next/font/google'
import './globals.css'

import { ReactNode } from 'react'
import ReactQueryProvider from '@/components/providers/ReactQueryProvider'

const bdStreetSign = localFont({
  src: '../../public/fonts/BDStreetSignSans_Variable.ttf',
  variable: '--font-bd-street',
  weight: '100 900',
  style: 'normal'
})

const signwriter = localFont({
  src: '../../public/fonts/00003-UTM-AS-Signwriter.ttf',
  variable: '--font-signwriter',
  weight: '100 900',
  style: 'normal'
})

const montserrat = Montserrat({
  subsets: ['latin'],
  variable: '--font-montserrat',
  display: 'swap'
})

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON> ánh trăng về'
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang='en'>
      <head>
        <meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no' />
        {/* Preload custom fonts for better performance and base64 conversion */}
        <link
          rel='preload'
          href='/fonts/BDStreetSignSans_Variable.ttf'
          as='font'
          type='font/ttf'
          crossOrigin='anonymous'
        />
        <link
          rel='preload'
          href='/fonts/00003-UTM-AS-Signwriter.ttf'
          as='font'
          type='font/ttf'
          crossOrigin='anonymous'
        />
      </head>
      <body className={`${bdStreetSign.variable} ${signwriter.variable} ${montserrat.variable} antialiased`}>
        <ReactQueryProvider>{children}</ReactQueryProvider>
      </body>
    </html>
  )
}
