// Font and image testing utilities for debugging loading issues
export const testFontLoading = async () => {
  console.log('=== Font Loading Test ===')

  // Test 1: Check if font files are accessible
  const fontPaths = ['/fonts/BDStreetSignSans_Variable.ttf', '/fonts/00003-UTM-AS-Signwriter.ttf']

  for (const fontPath of fontPaths) {
    try {
      const response = await fetch(fontPath)
      console.log(
        `✅ ${fontPath}: ${response.status} ${response.statusText} (${response.headers.get('content-length')} bytes)`
      )
    } catch (error) {
      console.error(`❌ ${fontPath}: Failed to fetch`, error)
    }
  }
}

// Test background image loading
export const testBackgroundImages = async () => {
  console.log('=== Background Image Loading Test ===')

  const imagePaths = ['/share/rankShare.webp', '/share/winShare.webp', '/share/shareBG.png']

  for (const imagePath of imagePaths) {
    try {
      const response = await fetch(imagePath)
      console.log(
        `✅ ${imagePath}: ${response.status} ${response.statusText} (${response.headers.get('content-length')} bytes)`
      )

      // Test image loading in DOM
      const img = new Image()
      img.crossOrigin = 'anonymous'
      await new Promise((resolve, reject) => {
        img.onload = () => {
          console.log(`✅ ${imagePath}: DOM load successful (${img.width}x${img.height})`)
          resolve(true)
        }
        img.onerror = () => {
          console.error(`❌ ${imagePath}: DOM load failed`)
          reject(new Error('Image load failed'))
        }
        img.src = imagePath
      })
    } catch (error) {
      console.error(`❌ ${imagePath}: Failed to load`, error)
    }
  }

  // Test 2: Check CSS font variables
  const testElement = document.createElement('div')
  testElement.style.fontFamily = 'var(--font-bd-street)'
  document.body.appendChild(testElement)

  const computedStyle = window.getComputedStyle(testElement)
  console.log('BD Street CSS variable resolves to:', computedStyle.fontFamily)

  testElement.style.fontFamily = 'var(--font-signwriter)'
  const computedStyle2 = window.getComputedStyle(testElement)
  console.log('Signwriter CSS variable resolves to:', computedStyle2.fontFamily)

  document.body.removeChild(testElement)

  // Test 3: Check if fonts are loaded in document.fonts
  if ('fonts' in document) {
    console.log(
      'Available fonts:',
      Array.from(document.fonts).map((font) => font.family)
    )

    // Check font loading status
    const bdStreetLoaded = document.fonts.check('16px "BD Street Sign Sans"')
    const signwriterLoaded = document.fonts.check('16px "UTM AS Signwriter"')

    console.log('BD Street font loaded:', bdStreetLoaded)
    console.log('Signwriter font loaded:', signwriterLoaded)
  }

  console.log('=== End Font Loading Test ===')
}

// Test font conversion to base64
export const testFontToBase64 = async (fontPath: string): Promise<boolean> => {
  try {
    const response = await fetch(fontPath)
    if (!response.ok) {
      console.error(`Font fetch failed: ${response.status} ${response.statusText}`)
      return false
    }

    const blob = await response.blob()
    console.log(`Font blob size: ${blob.size} bytes`)

    const base64 = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })

    console.log(`Font base64 length: ${base64.length}`)
    return base64.length > 0
  } catch (error) {
    console.error('Font to base64 conversion failed:', error)
    return false
  }
}

// Create a visual test for fonts
export const createFontVisualTest = () => {
  const container = document.createElement('div')
  container.style.position = 'fixed'
  container.style.top = '10px'
  container.style.right = '10px'
  container.style.background = 'white'
  container.style.border = '2px solid black'
  container.style.padding = '10px'
  container.style.zIndex = '9999'
  container.style.fontSize = '16px'
  container.style.maxWidth = '300px'

  const title = document.createElement('h3')
  title.textContent = 'Font Test'
  title.style.margin = '0 0 10px 0'
  container.appendChild(title)

  const bdStreetTest = document.createElement('div')
  bdStreetTest.className = 'font-bdstreet'
  bdStreetTest.textContent = 'BD Street Font Test - 123'
  bdStreetTest.style.marginBottom = '5px'
  container.appendChild(bdStreetTest)

  const signwriterTest = document.createElement('div')
  signwriterTest.className = 'font-signwriter'
  signwriterTest.textContent = 'Signwriter Font Test - 123'
  signwriterTest.style.marginBottom = '5px'
  container.appendChild(signwriterTest)

  const montserratTest = document.createElement('div')
  montserratTest.className = 'font-montserrat'
  montserratTest.textContent = 'Montserrat Font Test - 123'
  montserratTest.style.marginBottom = '10px'
  container.appendChild(montserratTest)

  const closeButton = document.createElement('button')
  closeButton.textContent = 'Close'
  closeButton.onclick = () => document.body.removeChild(container)
  container.appendChild(closeButton)

  document.body.appendChild(container)

  // Log computed styles
  setTimeout(() => {
    console.log('BD Street computed font:', window.getComputedStyle(bdStreetTest).fontFamily)
    console.log('Signwriter computed font:', window.getComputedStyle(signwriterTest).fontFamily)
    console.log('Montserrat computed font:', window.getComputedStyle(montserratTest).fontFamily)
  }, 100)
}
