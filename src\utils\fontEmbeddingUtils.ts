// Font embedding utilities for ShareDialog

export interface FontEmbedResult {
  hasBdStreetFont: boolean
  hasSignwriterFont: boolean
  styleElement: HTMLStyleElement
}

// Create embedded font styles with fallbacks
export const createEmbeddedFontStyles = (
  bdStreetFontBase64: string,
  signwriterFontBase64: string
): FontEmbedResult => {
  const hasBdStreetFont = bdStreetFontBase64.length > 0
  const hasSignwriterFont = signwriterFontBase64.length > 0
  
  if (!hasBdStreetFont) {
    console.error('BD Street font failed to load - text may not render correctly')
  }
  if (!hasSignwriterFont) {
    console.error('Signwriter font failed to load - text may not render correctly')
  }

  const styleElement = document.createElement('style')
  let fontStyles = ''
  
  // Only add font-face declarations if fonts were successfully loaded
  if (hasBdStreetFont) {
    fontStyles += `
      @font-face {
        font-family: 'BD Street Sign Sans Embedded';
        src: url('${bdStreetFontBase64}') format('truetype');
        font-display: block;
        font-weight: 100 900;
        font-style: normal;
      }
    `
  }
  
  if (hasSignwriterFont) {
    fontStyles += `
      @font-face {
        font-family: 'UTM AS Signwriter Embedded';
        src: url('${signwriterFontBase64}') format('truetype');
        font-display: block;
        font-weight: 100 900;
        font-style: normal;
      }
    `
  }
  
  // Add font class overrides with comprehensive fallbacks
  fontStyles += `
    .font-bdstreet {
      font-family: ${hasBdStreetFont ? "'BD Street Sign Sans Embedded', " : ""}'BD Street Sign Sans', var(--font-bd-street), 'Arial Black', Arial, sans-serif !important;
      font-weight: bold !important;
    }
    .font-signwriter {
      font-family: ${hasSignwriterFont ? "'UTM AS Signwriter Embedded', " : ""}'UTM AS Signwriter', var(--font-signwriter), 'Times New Roman', serif !important;
      font-weight: normal !important;
    }
    .font-montserrat {
      font-family: 'Montserrat', var(--font-montserrat), Arial, sans-serif !important;
    }
    
    /* Force font loading and rendering */
    * {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  `
  
  styleElement.textContent = fontStyles
  
  return {
    hasBdStreetFont,
    hasSignwriterFont,
    styleElement
  }
}

// Wait for fonts to be loaded and applied
export const waitForFontsLoad = async (
  hasBdStreetFont: boolean,
  hasSignwriterFont: boolean
): Promise<void> => {
  if (!hasBdStreetFont && !hasSignwriterFont) {
    return // No fonts to wait for
  }

  console.log('Waiting for fonts to load and apply...')
  
  // Force font loading by creating temporary elements
  const testContainer = document.createElement('div')
  testContainer.style.position = 'absolute'
  testContainer.style.left = '-9999px'
  testContainer.style.top = '-9999px'
  testContainer.style.visibility = 'hidden'
  
  const fontTestElements: HTMLElement[] = []
  
  if (hasBdStreetFont) {
    const bdStreetTest = document.createElement('span')
    bdStreetTest.className = 'font-bdstreet'
    bdStreetTest.textContent = 'Test BD Street Font'
    bdStreetTest.style.fontSize = '24px'
    testContainer.appendChild(bdStreetTest)
    fontTestElements.push(bdStreetTest)
  }
  
  if (hasSignwriterFont) {
    const signwriterTest = document.createElement('span')
    signwriterTest.className = 'font-signwriter'
    signwriterTest.textContent = 'Test Signwriter Font'
    signwriterTest.style.fontSize = '24px'
    testContainer.appendChild(signwriterTest)
    fontTestElements.push(signwriterTest)
  }
  
  document.body.appendChild(testContainer)
  
  // Wait for fonts to be applied (check computed styles)
  await new Promise<void>((resolve) => {
    let attempts = 0
    const maxAttempts = 20
    
    const checkFonts = () => {
      attempts++
      let fontsReady = true
      
      fontTestElements.forEach((element) => {
        const computedStyle = window.getComputedStyle(element)
        const fontFamily = computedStyle.fontFamily
        console.log(`Font check attempt ${attempts}: ${element.className} -> ${fontFamily}`)
        
        // Check if our embedded font is being used
        if (element.className.includes('bdstreet') && hasBdStreetFont) {
          fontsReady = fontsReady && fontFamily.includes('BD Street Sign Sans Embedded')
        }
        if (element.className.includes('signwriter') && hasSignwriterFont) {
          fontsReady = fontsReady && fontFamily.includes('UTM AS Signwriter Embedded')
        }
      })
      
      if (fontsReady || attempts >= maxAttempts) {
        document.body.removeChild(testContainer)
        console.log(`Fonts ready after ${attempts} attempts`)
        resolve()
      } else {
        setTimeout(checkFonts, 100)
      }
    }
    
    // Start checking after a small delay
    setTimeout(checkFonts, 100)
  })
}

// Mobile detection utility
export const isMobileDevice = (): boolean => {
  return typeof window !== 'undefined' && 
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}
