'use client'

import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

interface ShareWinContentProps {
  username: string | null
  avatar: string | null
  level: number
  score: number
}

const ShareWinContent: React.FC<ShareWinContentProps> = ({
  username,
  avatar,
  level,
  score
}) => {
  return (
    <>
      {/* Avatar */}
      <div className='absolute mx-auto mt-[4%] w-[25.8%]'>
        <AspectRatio ratio={1}>
          <div className='relative h-full w-full rounded-full p-[2px]'>
            {/* Gradient border as inline SVG */}
            <svg
              className='absolute inset-0 h-full w-full rounded-full'
              xmlns='http://www.w3.org/2000/svg'
              preserveAspectRatio='none'
            >
              <defs>
                <linearGradient id='avatar-border-win' x1='0' y1='0' x2='0' y2='1'>
                  <stop offset='0%' stopColor='#FFFF70' />
                  <stop offset='100%' stopColor='#F6B936' />
                </linearGradient>
              </defs>
              <rect width='100%' height='100%' rx='9999' fill='url(#avatar-border-win)' />
            </svg>

            {/* Avatar content */}
            <div className='relative h-full w-full rounded-full'>
              <Avatar className='h-full w-full'>
                <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </AspectRatio>
      </div>

      {/* Username */}
      <div className='absolute top-[47.5%] z-10 flex items-center'>
        <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
          {username}
        </span>
      </div>

      {/* Level */}
      <div className='absolute top-[61%] z-10 flex items-center'>
        <span className='font-signwriter text-[23.52px] text-[#FFFF70]'>Màn {level}</span>
      </div>

      {/* Score */}
      <div className='absolute top-[73%] z-10 flex flex-col items-center'>
        <span className='font-montserrat translate-y-[75%] text-[9.8px] font-black text-white'>
          Score
        </span>
        <span className='font-montserrat text-[32.34px] font-black text-[#FFFF70] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25),-0.98px_-0.98px_3.04px_rgba(0,0,0,0.21),0.98px_0.98px_0px_rgba(255,255,255,0.55)]'>
          {score}
        </span>
      </div>
    </>
  )
}

export default ShareWinContent
