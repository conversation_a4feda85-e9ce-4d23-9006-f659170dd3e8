'use client'

import { useState } from 'react'
import Image from 'next/image'

const MobileImageTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(false)

  const testImages = async () => {
    const results: string[] = []
    const images = [
      '/share/rankShare.webp',
      '/share/winShare.webp',
      '/share/shareBG.png'
    ]

    results.push('=== Mobile Image Loading Test ===')
    results.push(`User Agent: ${navigator.userAgent}`)
    results.push(`Screen: ${window.screen.width}x${window.screen.height}`)
    results.push(`Viewport: ${window.innerWidth}x${window.innerHeight}`)

    for (const imageSrc of images) {
      try {
        // Test 1: Fetch the image
        const response = await fetch(imageSrc)
        results.push(`✅ Fetch ${imageSrc}: ${response.status} (${response.headers.get('content-length')} bytes)`)

        // Test 2: Load as Image object
        const img = new Image()
        const loadResult = await new Promise<boolean>((resolve) => {
          img.onload = () => {
            results.push(`✅ Image load ${imageSrc}: ${img.width}x${img.height}`)
            resolve(true)
          }
          img.onerror = () => {
            results.push(`❌ Image load failed: ${imageSrc}`)
            resolve(false)
          }
          img.src = imageSrc
        })

        // Test 3: Check if image is cached
        if (img.complete) {
          results.push(`✅ Image cached: ${imageSrc}`)
        }

      } catch (error) {
        results.push(`❌ Error testing ${imageSrc}: ${error}`)
      }
    }

    results.push('=== Test Complete ===')
    setTestResults(results)
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '80px',
          right: '20px',
          zIndex: 9999,
          padding: '10px',
          background: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}
      >
        Test Mobile Images
      </button>
    )
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: '20px',
        left: '20px',
        right: '20px',
        maxHeight: '80vh',
        background: 'white',
        border: '2px solid #ccc',
        borderRadius: '8px',
        padding: '15px',
        zIndex: 9999,
        fontSize: '12px',
        overflow: 'auto',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0 }}>Mobile Image Test</h3>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            padding: '5px 10px',
            cursor: 'pointer'
          }}
        >
          Close
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={testImages}
          style={{
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '3px',
            padding: '8px 15px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Run Image Tests
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <h4>Visual Test:</h4>
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px', marginBottom: '10px' }}>
          <div>
            <p>Next.js Image (rankShare):</p>
            <div style={{ position: 'relative', width: '100px', height: '60px', border: '1px solid #ccc' }}>
              <Image
                src="/share/rankShare.webp"
                alt="Rank Share Test"
                fill
                style={{ objectFit: 'cover' }}
                onLoad={() => console.log('Next.js rankShare loaded')}
                onError={() => console.log('Next.js rankShare failed')}
              />
            </div>
          </div>
          <div>
            <p>Regular img (winShare):</p>
            <img
              src="/share/winShare.webp"
              alt="Win Share Test"
              style={{ width: '100px', height: '60px', objectFit: 'cover', border: '1px solid #ccc' }}
              onLoad={() => console.log('Regular img winShare loaded')}
              onError={() => console.log('Regular img winShare failed')}
            />
          </div>
        </div>
      </div>

      <div>
        <h4>Test Results:</h4>
        <div style={{ background: '#f8f9fa', padding: '10px', borderRadius: '3px', maxHeight: '300px', overflow: 'auto' }}>
          {testResults.length === 0 ? (
            <em>No tests run yet. Click &quot;Run Image Tests&quot; to start.</em>
          ) : (
            testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '3px', fontSize: '11px' }}>
                {result}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}

export default MobileImageTest
