// Image and font conversion utilities for ShareDialog

export interface ConversionResult {
  success: boolean
  data: string
  error?: string
}

// Convert image to base64 data URL
export const imageToBase64 = async (src: string): Promise<ConversionResult> => {
  try {
    const img = document.createElement('img')
    img.crossOrigin = 'anonymous'
    
    return new Promise((resolve) => {
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) {
            resolve({ success: false, data: '', error: 'Could not get canvas context' })
            return
          }
          canvas.width = img.width
          canvas.height = img.height
          ctx.drawImage(img, 0, 0)
          const dataURL = canvas.toDataURL('image/png')
          resolve({ success: true, data: dataURL })
        } catch (error) {
          resolve({ success: false, data: '', error: `Canvas conversion failed: ${error}` })
        }
      }
      img.onerror = () => resolve({ success: false, data: '', error: `Failed to load image: ${src}` })
      img.src = src
    })
  } catch (error) {
    return { success: false, data: '', error: `Image conversion failed: ${error}` }
  }
}

// Convert font to base64 data URL with multiple fallback attempts
export const fontToBase64 = async (fontPath: string): Promise<ConversionResult> => {
  try {
    const attempts = [
      fontPath,
      window.location.origin + fontPath,
      `${window.location.protocol}//${window.location.host}${fontPath}`
    ]

    for (const url of attempts) {
      try {
        console.log(`Attempting to load font from: ${url}`)
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            Accept: 'font/ttf,font/woff2,font/woff,*/*'
          }
        })
        
        if (!response.ok) {
          console.warn(`Font fetch failed with status ${response.status} for ${url}`)
          continue
        }
        
        const blob = await response.blob()
        console.log(`Font blob size: ${blob.size} bytes for ${url}`)
        
        return new Promise((resolve) => {
          const reader = new FileReader()
          reader.onload = () => {
            const result = reader.result as string
            console.log(`Font converted to base64, length: ${result.length}`)
            resolve({ success: true, data: result })
          }
          reader.onerror = () => {
            resolve({ success: false, data: '', error: `FileReader failed for ${url}` })
          }
          reader.readAsDataURL(blob)
        })
      } catch (error) {
        console.warn(`Failed to load font from ${url}:`, error)
        continue
      }
    }
    
    return { success: false, data: '', error: `All font loading attempts failed for ${fontPath}` }
  } catch (error) {
    return { success: false, data: '', error: `Font conversion failed: ${error}` }
  }
}

// Preload images for better performance
export const preloadImages = async (imagePaths: string[]): Promise<boolean[]> => {
  const promises = imagePaths.map(src => {
    return new Promise<boolean>((resolve) => {
      const img = new Image()
      img.onload = () => {
        console.log(`✅ Image preloaded: ${src}`)
        resolve(true)
      }
      img.onerror = () => {
        console.warn(`⚠️ Failed to preload image: ${src}`)
        resolve(false)
      }
      img.src = src
    })
  })
  
  return Promise.all(promises)
}

// Replace images in DOM with base64 versions
export const replaceImagesWithBase64 = async (
  targetElement: HTMLElement,
  backgroundImageBase64: string,
  avatarBase64: string
): Promise<{ element: HTMLImageElement; originalSrc: string }[]> => {
  const allImages = targetElement.querySelectorAll('img') as NodeListOf<HTMLImageElement>
  const originalSources: { element: HTMLImageElement; originalSrc: string }[] = []

  console.log('Found', allImages.length, 'images in total')

  allImages.forEach((img, index) => {
    const originalSrc = img.src
    originalSources.push({ element: img, originalSrc })

    console.log(`Image ${index + 1}:`, img.src)
    console.log(`  - Alt text:`, img.alt)
    console.log(`  - Classes:`, img.className)

    // Check if this is a background image
    const isBackgroundImage =
      img.src.includes('rankShare.webp') ||
      img.src.includes('winShare.webp') ||
      img.src.includes('/share/') ||
      img.src.includes('url=%2Fshare%2F') ||
      img.src.includes('url=/share/') ||
      img.alt === 'Share' ||
      img.alt === 'Share Background' ||
      img.alt === 'Share Background Fallback'

    // Check if this is an avatar image
    const isAvatarImage =
      avatarBase64 &&
      (img.src.startsWith('http') ||
        img.src.includes('avatar') ||
        img.alt?.toLowerCase().includes('user') ||
        img.className?.includes('avatar'))

    if (isBackgroundImage && backgroundImageBase64) {
      console.log('✅ Replacing background image:', img.src, 'with base64 length:', backgroundImageBase64.length)
      img.src = backgroundImageBase64
      img.onload = () => console.log('Background image loaded successfully')
      img.onerror = () => console.error('Background image failed to load')
    } else if (isBackgroundImage && !backgroundImageBase64) {
      console.warn('⚠️ Background image detected but no base64 available:', img.src)
    } else if (isAvatarImage && avatarBase64) {
      console.log('✅ Replacing avatar image:', img.src, 'with base64 length:', avatarBase64.length)
      img.src = avatarBase64
      img.onload = () => console.log('Avatar image loaded successfully')
    } else if (isAvatarImage && !avatarBase64) {
      console.warn('⚠️ Avatar image detected but no base64 available:', img.src)
    } else {
      console.log('ℹ️ Image not matched for replacement:', img.src, 'Alt:', img.alt)
    }
  })

  return originalSources
}

// Restore original image sources
export const restoreOriginalImages = (originalSources: { element: HTMLImageElement; originalSrc: string }[]) => {
  originalSources.forEach(({ element, originalSrc }) => {
    element.src = originalSrc
  })
}

// Wait for all images to load
export const waitForImagesLoad = async (images: NodeListOf<HTMLImageElement>): Promise<void> => {
  const imageLoadPromises = Array.from(images).map((img) => {
    return new Promise<void>((resolve) => {
      if (img.complete) {
        resolve()
      } else {
        img.onload = () => resolve()
        img.onerror = () => resolve() // Continue even if image fails to load
        setTimeout(() => resolve(), 3000) // Fallback timeout
      }
    })
  })

  await Promise.all(imageLoadPromises)
  console.log('All images loaded')
}
