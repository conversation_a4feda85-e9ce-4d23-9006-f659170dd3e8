'use client'

import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'

interface ShareRankContentProps {
  username: string | null
  avatar: string | null
  userRank: number
  currentLevel: number
  userTotalScore: number
}

const ShareRankContent: React.FC<ShareRankContentProps> = ({
  username,
  avatar,
  userRank,
  currentLevel,
  userTotalScore
}) => {
  return (
    <>
      {/* Avatar */}
      <div className='absolute left-[6%] mt-[6%] w-[12%]'>
        <AspectRatio ratio={1}>
          <div className='relative h-full w-full rounded-full p-[2px]'>
            {/* Gradient border as inline SVG */}
            <svg
              className='absolute inset-0 h-full w-full rounded-full'
              xmlns='http://www.w3.org/2000/svg'
              preserveAspectRatio='none'
            >
              <defs>
                <linearGradient id='avatar-border-rank' x1='0' y1='0' x2='0' y2='1'>
                  <stop offset='0%' stopColor='#FFFF70' />
                  <stop offset='100%' stopColor='#F6B936' />
                </linearGradient>
              </defs>
              <rect width='100%' height='100%' rx='9999' fill='url(#avatar-border-rank)' />
            </svg>

            {/* Avatar content */}
            <div className='relative h-full w-full rounded-full'>
              <Avatar className='h-full w-full'>
                <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
              </Avatar>
            </div>
          </div>
        </AspectRatio>
      </div>

      {/* Username */}
      <div className='absolute top-[8.5%] left-[22.5%] z-10 flex h-[17%] w-full items-center'>
        <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
          {username}
        </span>
      </div>

      {/* Stats Section */}
      <div className='font-montserrat absolute top-[34.49%] z-10 flex h-[50.6%] w-[91.81%] flex-col items-center'>
        {/* Top Section */}
        <div className='flex h-[29%] w-[91.66%] items-center'>
          <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[13.72px] font-bold text-transparent'>
            Thành tích
          </span>
        </div>

        {/* Separator */}
        <Separator orientation='horizontal' className='w-[91.66%] bg-[#535548]' />

        {/* Bottom Section */}
        <div className='flex h-[71%] w-[91.66%] flex-col justify-evenly text-[13.72px]'>
          <div className='flex w-full justify-between'>
            <span className='font-regular text-white'>Hạng hiện tại</span>
            <span className='font-black text-[#EBEBEB]'>{userRank.toLocaleString()}</span>
          </div>
          <div className='flex w-full justify-between'>
            <span className='font-regular text-white'>Màn chơi cao nhất</span>
            <span className='font-black text-[#EBEBEB]'>{currentLevel - 1}</span>
          </div>
          <div className='flex w-full justify-between'>
            <span className='font-regular text-white'>Tổng điểm</span>
            <span className='font-black text-[#EBEBEB]'>{userTotalScore.toLocaleString()}</span>
          </div>
        </div>
      </div>
    </>
  )
}

export default ShareRankContent
