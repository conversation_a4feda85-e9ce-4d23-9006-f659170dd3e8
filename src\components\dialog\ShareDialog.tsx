'use client'

import { Dialog, DialogContent } from '@/components/ui/dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Separator } from '@/components/ui/separator'
import Image from 'next/image'
import MediumDisabledButton from '@/assets/drawer/mediumDisabledButton.svg'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { useState, useRef, useEffect } from 'react'
import * as htmlToImage from 'html-to-image'
import { useGameStore } from '@/stores/useGameStore'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import { useRankingGameCampaign } from '@/hooks/useRankingGameCampaign'
import { useNativeStore } from '@/stores/useNativeStore'
import { PostMessageType } from '@/lib/postMessage'

interface ShareDialogProps {
  open: boolean
  onClose: () => void
  level: number
}

const ShareDialog: React.FC<ShareDialogProps> = ({ open, onClose, level }) => {
  const [text, setText] = useState('')
  const { gameCampaignId, username, avatar, passedLevels, currentLevel } = useGameStore()
  const targetRef = useRef<HTMLDivElement>(null) // reference for AspectRatio
  const [fontsPreloaded, setFontsPreloaded] = useState(false)

  // Preload fonts when dialog opens
  useEffect(() => {
    if (open && !fontsPreloaded) {
      const preloadFonts = async () => {
        console.log('Preloading fonts for ShareDialog...')
        try {
          const [bdStreetResult, signwriterResult] = await Promise.all([
            fontToBase64('/fonts/BDStreetSignSans_Variable.ttf'),
            fontToBase64('/fonts/00003-UTM-AS-Signwriter.ttf')
          ])

          console.log('Font preload results:', {
            bdStreet: bdStreetResult.length > 0 ? 'Success' : 'Failed',
            signwriter: signwriterResult.length > 0 ? 'Success' : 'Failed'
          })

          setFontsPreloaded(true)
        } catch (error) {
          console.error('Font preload failed:', error)
          setFontsPreloaded(true) // Continue anyway
        }
      }

      preloadFonts()
    }
  }, [open, fontsPreloaded])

  // Convert image to base64 data URL
  const imageToBase64 = async (src: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = document.createElement('img')
      img.crossOrigin = 'anonymous'
      img.onload = () => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) {
          reject(new Error('Could not get canvas context'))
          return
        }
        canvas.width = img.width
        canvas.height = img.height
        ctx.drawImage(img, 0, 0)
        try {
          const dataURL = canvas.toDataURL('image/png')
          resolve(dataURL)
        } catch (error) {
          reject(error)
        }
      }
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`))
      img.src = src
    })
  }

  // Convert font to base64 data URL with better error handling and fallbacks
  const fontToBase64 = async (fontPath: string): Promise<string> => {
    try {
      // Try multiple approaches for font loading
      const attempts = [
        fontPath,
        window.location.origin + fontPath,
        // Fallback to absolute path
        `${window.location.protocol}//${window.location.host}${fontPath}`
      ]

      for (const url of attempts) {
        try {
          console.log(`Attempting to load font from: ${url}`)
          const response = await fetch(url, {
            method: 'GET',
            headers: {
              Accept: 'font/ttf,font/woff2,font/woff,*/*'
            }
          })

          if (!response.ok) {
            console.warn(`Font fetch failed with status ${response.status} for ${url}`)
            continue
          }

          const blob = await response.blob()
          console.log(`Font blob size: ${blob.size} bytes for ${url}`)

          return new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.onload = () => {
              const result = reader.result as string
              console.log(`Font converted to base64, length: ${result.length}`)
              resolve(result)
            }
            reader.onerror = () => {
              console.error(`FileReader error for ${url}`)
              reject(new Error(`FileReader failed for ${url}`))
            }
            reader.readAsDataURL(blob)
          })
        } catch (error) {
          console.warn(`Failed to load font from ${url}:`, error)
          continue
        }
      }

      throw new Error(`All font loading attempts failed for ${fontPath}`)
    } catch (error) {
      console.error(`Failed to load font: ${fontPath}`, error)
      return ''
    }
  }

  const currentLevelInfo = passedLevels.find((lvl) => lvl.level === level)
  const score = currentLevelInfo?.score ?? 0

  const { variant } = useShareDialogStore()

  // Get ranking data for dynamic content in rank variant
  const { userId } = useNativeStore()
  const { data: rankingData } = useRankingGameCampaign(userId)

  // Find current user's ranking data
  const currentUserRanking = rankingData?.find((user) => user.name === username)
  const userRank = currentUserRanking?.rank ?? 0
  const userTotalScore = currentUserRanking?.score?.total ?? 0

  const handleExport = async () => {
    if (!targetRef.current) return
    try {
      // Convert all assets to base64 for proper canvas export
      const backgroundImageSrc = variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp'

      console.log('Starting export process...')

      // Convert background image to base64
      console.log('Converting background image:', backgroundImageSrc)
      let backgroundImageBase64 = ''
      try {
        backgroundImageBase64 = await imageToBase64(backgroundImageSrc)
        console.log('Background image converted successfully, length:', backgroundImageBase64.length)
      } catch (error) {
        console.error('Failed to convert background image:', error)
        // Try with absolute URL as fallback
        try {
          const absoluteUrl = window.location.origin + backgroundImageSrc
          console.log('Trying absolute URL:', absoluteUrl)
          backgroundImageBase64 = await imageToBase64(absoluteUrl)
          console.log('Background image converted with absolute URL, length:', backgroundImageBase64.length)
        } catch (fallbackError) {
          console.error('Fallback conversion also failed:', fallbackError)
        }
      }

      // Convert avatar to base64 if it exists
      let avatarBase64 = ''
      if (avatar) {
        try {
          console.log('Converting avatar:', avatar)
          avatarBase64 = await imageToBase64(avatar)
          console.log('Avatar converted, length:', avatarBase64.length)
        } catch (error) {
          console.warn('Failed to convert avatar to base64:', error)
        }
      }

      // Convert fonts to base64 and create embedded font CSS
      console.log('Converting fonts...')
      const [bdStreetFontBase64, signwriterFontBase64] = await Promise.all([
        fontToBase64('/fonts/BDStreetSignSans_Variable.ttf'),
        fontToBase64('/fonts/00003-UTM-AS-Signwriter.ttf')
      ])
      console.log('Fonts converted:', {
        bdStreet: bdStreetFontBase64.length,
        signwriter: signwriterFontBase64.length
      })

      // Check if fonts were successfully loaded
      const hasBdStreetFont = bdStreetFontBase64.length > 0
      const hasSignwriterFont = signwriterFontBase64.length > 0

      if (!hasBdStreetFont) {
        console.error('BD Street font failed to load - text may not render correctly')
      }
      if (!hasSignwriterFont) {
        console.error('Signwriter font failed to load - text may not render correctly')
      }

      // Temporarily replace images with base64 versions
      const targetElement = targetRef.current

      // Find all img elements (including those rendered by Next.js Image and Avatar components)
      const allImages = targetElement.querySelectorAll('img') as NodeListOf<HTMLImageElement>

      // Store original sources
      const originalSources: { element: HTMLImageElement; originalSrc: string }[] = []

      // Replace images with base64 versions
      console.log('Found', allImages.length, 'images in total')

      allImages.forEach((img, index) => {
        const originalSrc = img.src
        originalSources.push({ element: img, originalSrc })

        console.log(`Image ${index + 1}:`, img.src)
        console.log(`  - Alt text:`, img.alt)
        console.log(`  - Classes:`, img.className)

        // Check if this is a background image (Next.js optimized URLs or direct paths)
        const isBackgroundImage =
          img.src.includes('rankShare.webp') ||
          img.src.includes('winShare.webp') ||
          img.src.includes('/share/') ||
          img.src.includes('url=%2Fshare%2F') || // Next.js encoded URL
          img.src.includes('url=/share/') || // Next.js URL
          img.alt === 'Share' // Alt text from Image component

        // Check if this is an avatar image
        const isAvatarImage =
          avatarBase64 &&
          (img.src.startsWith('http') ||
            img.src.includes('avatar') ||
            img.alt?.toLowerCase().includes('user') ||
            img.className?.includes('avatar'))

        if (isBackgroundImage && backgroundImageBase64) {
          console.log('✅ Replacing background image:', img.src, 'with base64 length:', backgroundImageBase64.length)
          img.src = backgroundImageBase64
          // Force image to reload
          img.onload = () => console.log('Background image loaded successfully')
          img.onerror = () => console.error('Background image failed to load')
        } else if (isAvatarImage) {
          console.log('✅ Replacing avatar image:', img.src, 'with base64 length:', avatarBase64.length)
          img.src = avatarBase64
          // Force image to reload
          img.onload = () => console.log('Avatar image loaded successfully')
        } else {
          console.log('⚠️ Image not matched for replacement')
        }
      })

      // Create embedded font styles with better fallbacks
      const styleElement = document.createElement('style')
      let fontStyles = ''

      // Only add font-face declarations if fonts were successfully loaded
      if (hasBdStreetFont) {
        fontStyles += `
          @font-face {
            font-family: 'BD Street Sign Sans Embedded';
            src: url('${bdStreetFontBase64}') format('truetype');
            font-display: block;
            font-weight: 100 900;
            font-style: normal;
          }
        `
      }

      if (hasSignwriterFont) {
        fontStyles += `
          @font-face {
            font-family: 'UTM AS Signwriter Embedded';
            src: url('${signwriterFontBase64}') format('truetype');
            font-display: block;
            font-weight: 100 900;
            font-style: normal;
          }
        `
      }

      // Add font class overrides with comprehensive fallbacks
      fontStyles += `
        .font-bdstreet {
          font-family: ${hasBdStreetFont ? "'BD Street Sign Sans Embedded', " : ''}'BD Street Sign Sans', var(--font-bd-street), 'Arial Black', Arial, sans-serif !important;
          font-weight: bold !important;
        }
        .font-signwriter {
          font-family: ${hasSignwriterFont ? "'UTM AS Signwriter Embedded', " : ''}'UTM AS Signwriter', var(--font-signwriter), 'Times New Roman', serif !important;
          font-weight: normal !important;
        }
        .font-montserrat {
          font-family: 'Montserrat', var(--font-montserrat), Arial, sans-serif !important;
        }

        /* Force font loading and rendering */
        * {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
      `

      styleElement.textContent = fontStyles

      // Add background image override if conversion was successful
      if (backgroundImageBase64) {
        styleElement.textContent += `
          /* Override Next.js Image with base64 background */
          img[alt="Share"] {
            content: url('${backgroundImageBase64}') !important;
          }
          /* Alternative approach using background-image */
          .share-background-override {
            background-image: url('${backgroundImageBase64}') !important;
            background-size: cover !important;
            background-position: center !important;
          }
        `

        // Also try to apply the background class to the container
        const shareContainer = targetElement.querySelector('[class*="relative"]')
        if (shareContainer) {
          shareContainer.classList.add('share-background-override')
          console.log('Added background override class to container')
        }
      }

      document.head.appendChild(styleElement)

      // Wait for fonts to be loaded and applied
      console.log('Waiting for fonts to load and apply...')

      // Force font loading by creating temporary elements
      const fontTestElements: HTMLElement[] = []
      if (hasBdStreetFont || hasSignwriterFont) {
        const testContainer = document.createElement('div')
        testContainer.style.position = 'absolute'
        testContainer.style.left = '-9999px'
        testContainer.style.top = '-9999px'
        testContainer.style.visibility = 'hidden'

        if (hasBdStreetFont) {
          const bdStreetTest = document.createElement('span')
          bdStreetTest.className = 'font-bdstreet'
          bdStreetTest.textContent = 'Test BD Street Font'
          bdStreetTest.style.fontSize = '24px'
          testContainer.appendChild(bdStreetTest)
          fontTestElements.push(bdStreetTest)
        }

        if (hasSignwriterFont) {
          const signwriterTest = document.createElement('span')
          signwriterTest.className = 'font-signwriter'
          signwriterTest.textContent = 'Test Signwriter Font'
          signwriterTest.style.fontSize = '24px'
          testContainer.appendChild(signwriterTest)
          fontTestElements.push(signwriterTest)
        }

        document.body.appendChild(testContainer)

        // Wait for fonts to be applied (check computed styles)
        await new Promise<void>((resolve) => {
          let attempts = 0
          const maxAttempts = 20

          const checkFonts = () => {
            attempts++
            let fontsReady = true

            fontTestElements.forEach((element) => {
              const computedStyle = window.getComputedStyle(element)
              const fontFamily = computedStyle.fontFamily
              console.log(`Font check attempt ${attempts}: ${element.className} -> ${fontFamily}`)

              // Check if our embedded font is being used
              if (element.className.includes('bdstreet') && hasBdStreetFont) {
                fontsReady = fontsReady && fontFamily.includes('BD Street Sign Sans Embedded')
              }
              if (element.className.includes('signwriter') && hasSignwriterFont) {
                fontsReady = fontsReady && fontFamily.includes('UTM AS Signwriter Embedded')
              }
            })

            if (fontsReady || attempts >= maxAttempts) {
              document.body.removeChild(testContainer)
              console.log(`Fonts ready after ${attempts} attempts`)
              resolve()
            } else {
              setTimeout(checkFonts, 100)
            }
          }

          // Start checking after a small delay
          setTimeout(checkFonts, 100)
        })
      }

      // Wait for images and fonts to load (increased time for base64 images)
      console.log('Waiting for images to load...')

      // Wait for all images to load
      const imageLoadPromises = Array.from(allImages).map((img) => {
        return new Promise<void>((resolve) => {
          if (img.complete) {
            resolve()
          } else {
            img.onload = () => resolve()
            img.onerror = () => resolve() // Continue even if image fails to load
            // Fallback timeout
            setTimeout(() => resolve(), 3000)
          }
        })
      })

      await Promise.all(imageLoadPromises)
      console.log('All images loaded, proceeding with export...')

      // Additional delay to ensure rendering is complete with fonts
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Export with embedded assets
      const dataUrl = await htmlToImage.toPng(targetRef.current, {
        cacheBust: true,
        quality: 1.0,
        pixelRatio: 3, // Higher resolution for better quality
        backgroundColor: 'transparent',
        skipFonts: false,
        width: targetRef.current.offsetWidth,
        height: targetRef.current.offsetHeight
      })

      // Restore original images
      originalSources.forEach(({ element, originalSrc }) => {
        element.src = originalSrc
      })

      // Remove embedded styles
      document.head.removeChild(styleElement)

      const base64Only = dataUrl.replace(/^data:image\/png;base64,/, '')
      console.log('Generated base64 image length:', base64Only.length)

      // Log font usage in the final image
      console.log('Font embedding summary:', {
        bdStreetEmbedded: hasBdStreetFont,
        signwriterEmbedded: hasSignwriterFont,
        imageSize: base64Only.length
      })

      // Prepare message data based on variant
      const messageData = {
        type: PostMessageType.SHARE_COMMUNITY,
        gameCampaignId,
        levelNumber: variant === 'rank' ? 0 : level,
        content: text,
        imageBase64: base64Only,
        shareType: variant === 'rank' ? 'BY_RANK' : 'BY_LEVEL'
      }

      // Prepare and log message data
      console.log('Prepared share message:', messageData)

      // Send message to native app
      if (window.ReactNativeWebView?.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify(messageData))
      }

      console.log('Share message sent:', messageData)

      // Close dialog after successful share
      onClose()
    } catch (err) {
      console.error('Export failed', err)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value.slice(0, 100) // max 100 chars
    setText(value)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className='h-full border-none bg-transparent p-0 shadow-none'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        {/* Top Bar */}
        <div className='absolute top-0 z-10 flex h-[12.3%] w-full items-end justify-center'>
          <span className='font-bdstreet text-border-orange text-[32px]'>Chia sẻ cộng đồng</span>
        </div>

        {/* Main Center Content */}
        <div className='flex h-full w-full items-center justify-center'>
          <AspectRatio ratio={1560 / 2184} className='relative flex justify-center'>
            <Image src='/share/shareBG.png' alt='Share' fill quality={100} />

            {/* Inner AspectRatio centered vertically */}
            <div className='absolute inset-0 flex flex-col items-center justify-center'>
              <AspectRatio
                ref={targetRef}
                ratio={1560 / 1076}
                className='relative flex flex-col items-center shadow-[3px_4px_150px_15px_rgba(255,255,112,0.3)]'
                style={{
                  backgroundImage: `url(${variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp'})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  fontFamily: 'var(--font-bd-street), var(--font-signwriter), var(--font-montserrat), sans-serif'
                }}
              >
                {/* different background already set above; inner content changes by variant */}
                {variant === 'rank' ? (
                  <>
                    {/* Avatar */}
                    <div className='absolute left-[6%] mt-[6%] w-[12%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full p-[2px]'>
                          {/* Gradient border as inline SVG */}
                          <svg
                            className='absolute inset-0 h-full w-full rounded-full'
                            xmlns='http://www.w3.org/2000/svg'
                            preserveAspectRatio='none'
                          >
                            <defs>
                              <linearGradient id='avatar-border' x1='0' y1='0' x2='0' y2='1'>
                                <stop offset='0%' stopColor='#FFFF70' />
                                <stop offset='100%' stopColor='#F6B936' />
                              </linearGradient>
                            </defs>
                            <rect width='100%' height='100%' rx='9999' fill='url(#avatar-border)' />
                          </svg>

                          {/* Avatar content */}
                          <div className='relative h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    <div className='absolute top-[8.5%] left-[22.5%] z-10 flex h-[17%] w-full items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>

                    <div className='font-montserrat absolute top-[34.49%] z-10 flex h-[50.6%] w-[91.81%] flex-col items-center'>
                      {/* Top Section */}
                      <div className='flex h-[29%] w-[91.66%] items-center'>
                        <span className='bg-gradient-to-b from-[#FFFF70] to-[#F6B936] bg-clip-text text-[13.72px] font-bold text-transparent'>
                          Thành tích
                        </span>
                      </div>

                      {/* Separator */}
                      <Separator orientation='horizontal' className='w-[91.66%] bg-[#535548]' />

                      {/* Bottom Section */}
                      <div className='flex h-[71%] w-[91.66%] flex-col justify-evenly text-[13.72px]'>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Hạng hiện tại</span>
                          <span className='font-black text-[#EBEBEB]'>{userRank.toLocaleString()}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Màn chơi cao nhất</span>
                          <span className='font-black text-[#EBEBEB]'>{currentLevel - 1}</span>
                        </div>
                        <div className='flex w-full justify-between'>
                          <span className='font-regular text-white'>Tổng điểm</span>
                          <span className='font-black text-[#EBEBEB]'>{userTotalScore.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    {/* Avatar */}
                    <div className='absolute mx-auto mt-[4%] w-[25.8%]'>
                      <AspectRatio ratio={1}>
                        <div className='relative h-full w-full rounded-full p-[2px]'>
                          {/* Gradient border as inline SVG */}
                          <svg
                            className='absolute inset-0 h-full w-full rounded-full'
                            xmlns='http://www.w3.org/2000/svg'
                            preserveAspectRatio='none'
                          >
                            <defs>
                              <linearGradient id='avatar-border' x1='0' y1='0' x2='0' y2='1'>
                                <stop offset='0%' stopColor='#FFFF70' />
                                <stop offset='100%' stopColor='#F6B936' />
                              </linearGradient>
                            </defs>
                            <rect width='100%' height='100%' rx='9999' fill='url(#avatar-border)' />
                          </svg>

                          {/* Avatar content */}
                          <div className='relative h-full w-full rounded-full'>
                            <Avatar className='h-full w-full'>
                              <AvatarImage src={avatar ?? ''} alt={username ?? 'User'} />
                              <AvatarFallback>{username?.slice(0, 2) ?? '??'}</AvatarFallback>
                            </Avatar>
                          </div>
                        </div>
                      </AspectRatio>
                    </div>

                    {/* Texts below avatar */}
                    <div className='absolute top-[47.5%] z-10 flex items-center'>
                      <span className='font-bdstreet text-[23.52px] text-[#FF5500] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25)]'>
                        {username}
                      </span>
                    </div>
                    <div className='absolute top-[61%] z-10 flex items-center'>
                      <span className='font-signwriter text-[23.52px] text-[#FFFF70]'>Màn {level}</span>
                    </div>
                    <div className='absolute top-[73%] z-10 flex flex-col items-center'>
                      <span className='font-montserrat translate-y-[75%] text-[9.8px] font-black text-white'>
                        Score
                      </span>
                      <span className='font-montserrat text-[32.34px] font-black text-[#FFFF70] [text-shadow:1.96px_1.96px_3.92px_rgba(0,0,0,0.25),-0.98px_-0.98px_3.04px_rgba(0,0,0,0.21),0.98px_0.98px_0px_rgba(255,255,255,0.55)]'>
                        {score}
                      </span>
                    </div>
                  </>
                )}
              </AspectRatio>
            </div>
          </AspectRatio>
        </div>

        {/* Bottom Container */}
        <div className='absolute bottom-0 z-10 flex h-[22%] w-full flex-col items-center'>
          <textarea
            value={text}
            onChange={handleChange}
            maxLength={100}
            placeholder='Cảm nghĩ của bạn...'
            className={`font-montserrat h-[39.2%] w-[92.5%] resize-none rounded-2xl border bg-white/10 px-[3%] py-[3%] text-[16px] tracking-tight text-white backdrop-blur-xs outline-none placeholder:text-[#BAB9B9] ${
              text.length > 0 ? 'border-[#F6B936]' : 'border-white/30'
            }`}
          />

          {/* Bottom Row */}
          <div className='flex h-[60.8%] w-full items-center justify-center space-x-[10%]'>
            <div onClick={onClose} className='relative w-[35%] cursor-pointer'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <Image src='/share/mediumerOrangeButton.png' alt='Close' fill quality={100} />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Đóng
              </span>
            </div>

            <div className='relative w-[35%]' onClick={text.length > 0 ? handleExport : undefined}>
              <AspectRatio ratio={137 / 48} className='relative'>
                {text.length > 0 ? (
                  <MediumGreenButton className='h-full w-full' />
                ) : (
                  <MediumDisabledButton className='h-full w-full' />
                )}
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                {fontsPreloaded ? 'Chia sẻ' : 'Đang tải...'}
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ShareDialog
