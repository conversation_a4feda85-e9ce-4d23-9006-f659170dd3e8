'use client'

import React, { useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import MediumGreenButton from '@/assets/drawer/mediumGreenButton.svg'
import MediumDisabledButton from '@/assets/drawer/mediumDisabledButton.svg'
import ShareBackground from '@/components/share/ShareBackground'
import ShareRankContent from '@/components/share/ShareRankContent'
import ShareWinContent from '@/components/share/ShareWinContent'
import { useShareExport } from '@/hooks/useShareExport'
import { useShareDialogStore } from '@/stores/useShareDialogStore'
import { useNativeStore } from '@/stores/useNativeStore'
import { useRankingGameCampaign } from '@/hooks/useRankingGameCampaign'
import { preloadImages } from '@/utils/imageConversionUtils'

interface ShareDialogProps {
  open: boolean
  onClose: () => void
  username?: string
  avatar?: string
  level: number
  gameCampaignId: string
  passedLevels: Array<{ level: number; score: number }>
  currentLevel: number
}

const ShareDialog: React.FC<ShareDialogProps> = ({
  open,
  onClose,
  username,
  avatar,
  level,
  gameCampaignId,
  passedLevels,
  currentLevel
}) => {
  const [text, setText] = useState('')
  const [fontsPreloaded, setFontsPreloaded] = useState(false)
  const targetRef = useRef<HTMLDivElement>(null!)

  // Get current level score
  const currentLevelInfo = passedLevels.find((lvl) => lvl.level === level)
  const score = currentLevelInfo?.score ?? 0

  const { variant } = useShareDialogStore()

  // Get ranking data for dynamic content in rank variant
  const { userId } = useNativeStore()
  const { data: rankingData } = useRankingGameCampaign(userId)

  // Find current user's ranking data
  const currentUserRanking = rankingData?.find((user) => user.name === username)
  const userRank = currentUserRanking?.rank ?? 0
  const userTotalScore = currentUserRanking?.score?.total ?? 0

  // Use the refactored export hook
  const { handleExport } = useShareExport({
    gameCampaignId,
    variant,
    avatar: avatar ?? null,
    onClose
  })

  // Preload assets when dialog opens
  useEffect(() => {
    if (open) {
      const preloadAssets = async () => {
        console.log('Preloading fonts and images for ShareDialog...')

        // Preload background images
        const backgroundImages = ['/share/rankShare.webp', '/share/winShare.webp']
        await preloadImages(backgroundImages)

        setFontsPreloaded(true)
        console.log('ShareDialog assets preloaded successfully')
      }

      preloadAssets()
    }
  }, [open])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value.slice(0, 100) // max 100 chars
    setText(value)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className='h-full border-none bg-transparent p-0 shadow-none'
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        {/* Top Bar */}
        <div className='absolute top-0 z-10 flex h-[12.3%] w-full items-end justify-center'>
          <span className='font-bdstreet text-border-orange text-[32px]'>Chia sẻ cộng đồng</span>
        </div>

        {/* Main Center Content */}
        <div className='flex h-full w-full items-center justify-center'>
          <AspectRatio ratio={1560 / 2184} className='relative flex justify-center'>
            <Image src='/share/shareBG.png' alt='Share' fill quality={100} />

            {/* Inner AspectRatio centered vertically */}
            <div className='absolute inset-0 flex flex-col items-center justify-center'>
              <AspectRatio
                ref={targetRef}
                ratio={1560 / 1076}
                className='relative flex flex-col items-center shadow-[3px_4px_150px_15px_rgba(255,255,112,0.3)]'
                style={{
                  fontFamily: 'var(--font-bd-street), var(--font-signwriter), var(--font-montserrat), sans-serif'
                }}
              >
                {/* Background Component */}
                <ShareBackground variant={variant} />

                {/* Content based on variant */}
                {variant === 'rank' ? (
                  <ShareRankContent
                    username={username ?? null}
                    avatar={avatar ?? null}
                    userRank={userRank}
                    currentLevel={currentLevel}
                    userTotalScore={userTotalScore}
                  />
                ) : (
                  <ShareWinContent username={username ?? null} avatar={avatar ?? null} level={level} score={score} />
                )}
              </AspectRatio>
            </div>
          </AspectRatio>
        </div>

        {/* Bottom Container */}
        <div className='absolute bottom-0 z-10 flex h-[22%] w-full flex-col items-center'>
          <textarea
            value={text}
            onChange={handleChange}
            maxLength={100}
            placeholder='Cảm nghĩ của bạn...'
            className={`font-montserrat h-[39.2%] w-[92.5%] resize-none rounded-2xl border bg-white/10 px-[3%] py-[3%] text-[16px] tracking-tight text-white backdrop-blur-xs outline-none placeholder:text-[#BAB9B9] ${
              text.length > 0 ? 'border-[#F6B936]' : 'border-white/30'
            }`}
          />

          {/* Bottom Row */}
          <div className='flex h-[60.8%] w-full items-center justify-center space-x-[10%]'>
            <div onClick={onClose} className='relative w-[35%] cursor-pointer'>
              <AspectRatio ratio={137 / 48} className='relative'>
                <Image src='/share/mediumerOrangeButton.png' alt='Close' fill quality={100} />
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                Đóng
              </span>
            </div>

            <div
              className='relative w-[35%]'
              onClick={text.length > 0 ? () => handleExport(targetRef, text, level) : undefined}
            >
              <AspectRatio ratio={137 / 48} className='relative'>
                {text.length > 0 ? (
                  <MediumGreenButton className='h-full w-full' />
                ) : (
                  <MediumDisabledButton className='h-full w-full' />
                )}
              </AspectRatio>
              <span className='font-bdstreet text-shadow-orange-btn absolute inset-0 flex items-center justify-center text-[20px] font-bold text-white'>
                {fontsPreloaded ? 'Chia sẻ' : 'Đang tải...'}
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ShareDialog
