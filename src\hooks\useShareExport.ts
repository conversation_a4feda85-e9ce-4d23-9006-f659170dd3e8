import { useCallback } from 'react'
import * as htmlToImage from 'html-to-image'
import { PostMessageType } from '@/lib/postMessage'
import {
  imageToBase64,
  fontToBase64,
  replaceImagesWithBase64,
  restoreOriginalImages,
  waitForImagesLoad,
  type ConversionResult
} from '@/utils/imageConversionUtils'
import { createEmbeddedFontStyles, waitForFontsLoad, isMobileDevice } from '@/utils/fontEmbeddingUtils'

interface UseShareExportProps {
  gameCampaignId: string | null
  variant: 'rank' | 'win'
  avatar: string | null
  onClose: () => void
}

export const useShareExport = ({ gameCampaignId, variant, avatar, onClose }: UseShareExportProps) => {
  const handleExport = useCallback(
    async (targetRef: React.RefObject<HTMLDivElement>, text: string, level: number) => {
      if (!targetRef.current) return

      try {
        const backgroundImageSrc = variant === 'rank' ? '/share/rankShare.webp' : '/share/winShare.webp'
        const isMobile = isMobileDevice()

        console.log(`Starting export process... (Mobile: ${isMobile})`)

        // Convert background image to base64
        console.log('Converting background image:', backgroundImageSrc)
        const backgroundResult = await imageToBase64(backgroundImageSrc)
        if (!backgroundResult.success) {
          console.error('Failed to convert background image:', backgroundResult.error)
        }

        // Convert avatar to base64 if it exists
        let avatarResult: ConversionResult = { success: false, data: '' }
        if (avatar) {
          console.log('Converting avatar:', avatar)
          avatarResult = await imageToBase64(avatar)
          if (!avatarResult.success) {
            console.warn('Failed to convert avatar to base64:', avatarResult.error || 'Unknown error')
          }
        }

        // Convert fonts to base64
        console.log('Converting fonts...')
        const [bdStreetResult, signwriterResult] = await Promise.all([
          fontToBase64('/fonts/BDStreetSignSans_Variable.ttf'),
          fontToBase64('/fonts/00003-UTM-AS-Signwriter.ttf')
        ])

        console.log('Font conversion results:', {
          bdStreet: bdStreetResult.success ? 'Success' : 'Failed',
          signwriter: signwriterResult.success ? 'Success' : 'Failed'
        })

        // Create embedded font styles
        const { hasBdStreetFont, hasSignwriterFont, styleElement } = createEmbeddedFontStyles(
          bdStreetResult.data,
          signwriterResult.data
        )

        // Add styles to document
        document.head.appendChild(styleElement)

        // Wait for fonts to be loaded and applied
        await waitForFontsLoad(hasBdStreetFont, hasSignwriterFont)

        // Replace images with base64 versions
        const targetElement = targetRef.current
        const originalSources = await replaceImagesWithBase64(targetElement, backgroundResult.data, avatarResult.data)

        // Wait for all images to load
        const allImages = targetElement.querySelectorAll('img') as NodeListOf<HTMLImageElement>
        await waitForImagesLoad(allImages)

        // Additional delay to ensure rendering is complete
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Export with embedded assets
        console.log('Starting PNG export with htmlToImage...')
        const dataUrl = await htmlToImage.toPng(targetRef.current, {
          cacheBust: true,
          quality: 1.0,
          pixelRatio: 3,
          backgroundColor: 'transparent',
          skipFonts: false,
          width: targetRef.current.offsetWidth,
          height: targetRef.current.offsetHeight
        })

        // Restore original images
        restoreOriginalImages(originalSources)

        // Remove embedded styles
        document.head.removeChild(styleElement)

        const base64Only = dataUrl.replace(/^data:image\/png;base64,/, '')

        // Log comprehensive export summary
        console.log('Export summary:', {
          backgroundImageConverted: backgroundResult.success,
          backgroundImageSize: backgroundResult.data.length,
          bdStreetFontEmbedded: hasBdStreetFont,
          signwriterFontEmbedded: hasSignwriterFont,
          avatarConverted: avatarResult.success,
          finalImageSize: base64Only.length,
          variant: variant,
          isMobile: isMobile
        })

        // Download for testing (remove this in production)
        const downloadLink = document.createElement('a')
        downloadLink.href = dataUrl
        downloadLink.download = variant === 'rank' ? 'rank-share.png' : 'win-share.png'
        document.body.appendChild(downloadLink)
        downloadLink.click()
        document.body.removeChild(downloadLink)

        // Prepare message data based on variant
        const messageData = {
          type: PostMessageType.SHARE_COMMUNITY,
          gameCampaignId,
          levelNumber: variant === 'rank' ? 0 : level,
          content: text,
          imageBase64: base64Only,
          shareType: variant === 'rank' ? 'BY_RANK' : 'BY_LEVEL'
        }

        // Send message to native app
        if (window.ReactNativeWebView?.postMessage) {
          window.ReactNativeWebView.postMessage(JSON.stringify(messageData))
        }

        console.log('Share message sent:', messageData)

        // Close dialog after successful share
        onClose()
      } catch (err) {
        console.error('Export failed', err)
      }
    },
    [gameCampaignId, variant, avatar, onClose]
  )

  return { handleExport }
}
